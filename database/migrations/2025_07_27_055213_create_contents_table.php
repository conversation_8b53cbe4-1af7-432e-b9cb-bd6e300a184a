<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contents', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\ContentCategory::class);
            $table->foreignIdFor(\App\Models\User::class);
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('content');
            $table->string('description')->nullable();
            $table->string('image_alt')->nullable();

            $table->timestamp('published_at')->nullable();
            $table->enum('status', ['draft', 'published'])->default('draft');

            $table->json('settings')->nullable(); // например, цветовая схема

            $table->string('meta_title');
            $table->string('meta_description');
            $table->string('meta_keywords')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contents');
    }
};
