<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('content_blocks', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(\App\Models\Content::class);

            $table->enum('type', [
                'text', 'image', 'gallery', 'video', 'quote', 'embed',
            ])->index();

            $table->json('data');              // все данные блока
            $table->unsignedInteger('position'); // порядок вывода

            $table->timestamps();

            $table->unique(['content_id', 'position']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('content_blocks');
    }
};
