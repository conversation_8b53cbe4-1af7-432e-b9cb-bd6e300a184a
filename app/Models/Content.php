<?php

namespace App\Models;

use App\Traits\HasSlug;
use App\Traits\ImagesConversion350Trait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Content extends Model
{
    use HasFactory;
    use HasSlug;
    use ImagesConversion350Trait;

    protected $fillable = [
        'content_category_id',
        'user_id',
        'title',
        'slug',
        'description',
        'content',
        'image',
        'image_alt',
        'published_at',
        'status',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    protected $casts = [
        'published_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(ContentCategory::class, 'content_category_id');
    }

    public function blocks(): HasMany
    {
        return $this->hasMany(ContentBlock::class)
            ->orderBy('position');
    }
}
