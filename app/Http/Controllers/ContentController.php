<?php

namespace App\Http\Controllers;

use App\Http\Resources\Content\ContentResource;
use App\Models\Content;
use Illuminate\Http\Request;

class ContentController extends Controller
{
    /**
     * Список опубликованных материалов.
     * ?category=slug — фильтрация по категории
     * ?page=2 — пагинация (по умолчанию 10 на страницу)
     */
    public function index(Request $request)
    {
        $contents = Content::with('category')        // подтягиваем категорию
            ->withCount('blocks')                    // сколько блоков содержит
            ->where('status', 'published')           // только опубликованные
            ->when(
                $request->filled('category'),
                fn ($q) => $q->whereHas('category', function ($q) use ($request) {
                    $q->where('slug', $request->category);
                })
            )
            ->latest('published_at')
            ->paginate(10);

        return ContentResource::collection($contents);
    }

    /**
     * Детальная карточка материала + блоки.
     * Используется Route Model Binding по slug.
     */
    public function show(Content $content)
    {
        // защита от просмотра черновиков
        abort_unless($content->status === 'published', 404);

        $content->load(['category', 'blocks']);      // orderBy('position') уже в модели

        return new ContentResource($content);
    }
}
