<?php

namespace App\Services\Admin;

use App\Models\Content;
use App\Models\ContentBlock;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class ContentService
{
    /**
     * Получить список контента с фильтрами
     */
    public function getContentList(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        return Content::with(['category'])
            ->when(
                !empty($filters['status']),
                fn (Builder $query) => $query->where('status', $filters['status'])
            )
            ->when(
                !empty($filters['search']),
                fn (Builder $query) => $query->where('title', 'like', "%{$filters['search']}%")
            )
            ->when(
                !empty($filters['category']),
                fn (Builder $query) => $query->whereHas('category',
                    fn (Builder $q) => $q->where('slug', $filters['category'])
                )
            )
            ->when(
                !empty($filters['type']),
                fn (Builder $query) => $query->where('type', $filters['type'])
            )
            ->latest('updated_at')
            ->paginate($perPage);
    }

    /**
     * Создать новый контент с блоками
     */
    public function createContent(array $data): Content
    {
        return DB::transaction(function () use ($data) {
            $blocks = $data['blocks'] ?? [];
            unset($data['blocks']);

            $content = Content::create($data);

            if (!empty($blocks)) {
                $this->syncBlocks($content, $blocks);
            }

            return $content->load(['category', 'blocks']);
        });
    }

    /**
     * Обновить контент с блоками
     */
    public function updateContent(Content $content, array $data): Content
    {
        return DB::transaction(function () use ($content, $data) {
            $blocks = $data['blocks'] ?? null;
            unset($data['blocks']);

            $content->update($data);

            if ($blocks !== null) {
                $this->syncBlocks($content, $blocks);
            }

            return $content->load(['category', 'blocks']);
        });
    }

    /**
     * Удалить контент
     */
    public function deleteContent(Content $content): bool
    {
        return DB::transaction(function () use ($content) {
            // Удаляем все блоки
            $content->blocks()->delete();

            // Удаляем сам контент
            return $content->delete();
        });
    }

    /**
     * Синхронизация блоков контента
     *
     * @param Content $content
     * @param array $blocks
     */
    protected function syncBlocks(Content $content, array $blocks): void
    {
        // Получаем ID существующих блоков
        $existingIds = collect($blocks)
            ->pluck('id')
            ->filter()
            ->values()
            ->all();

        // Удаляем блоки, которых нет в новом списке
        $content->blocks()
            ->whereNotIn('id', $existingIds)
            ->delete();

        // Создаем или обновляем блоки
        foreach ($blocks as $blockData) {
            $content->blocks()->updateOrCreate(
                ['id' => $blockData['id'] ?? null],
                [
                    'type'     => $blockData['type'],
                    'data'     => $blockData['data'],
                    'position' => $blockData['position'],
                ]
            );
        }
    }

    /**
     * Получить статистику по контенту
     */
    public function getContentStats(): array
    {
        return [
            'total'     => Content::count(),
            'published' => Content::where('status', 'published')->count(),
            'draft'     => Content::where('status', 'draft')->count(),
            'by_type'   => Content::select('type', DB::raw('count(*) as count'))
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray(),
        ];
    }

    /**
     * Массовое изменение статуса
     */
    public function bulkUpdateStatus(array $contentIds, string $status): int
    {
        return Content::whereIn('id', $contentIds)
            ->update(['status' => $status]);
    }

    /**
     * Дублировать контент
     */
    public function duplicateContent(Content $content): Content
    {
        return DB::transaction(function () use ($content) {
            // Создаем копию контента
            $newContent = $content->replicate();
            $newContent->title = $content->title . ' (копия)';
            $newContent->slug = $content->slug . '-copy-' . time();
            $newContent->status = 'draft';
            $newContent->published_at = null;
            $newContent->save();

            // Копируем блоки
            foreach ($content->blocks as $block) {
                $newBlock = $block->replicate();
                $newBlock->content_id = $newContent->id;
                $newBlock->save();
            }

            return $newContent->load(['category', 'blocks']);
        });
    }
}
